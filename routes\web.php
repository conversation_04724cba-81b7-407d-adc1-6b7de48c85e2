<?php

use App\Http\Controllers\ProfileController;
use App\Http\Controllers\PostController;
use Illuminate\Support\Facades\Route;

// الصفحة الرئيسية توجه إلى تسجيل الدخول
Route::get('/', function () {
    return redirect()->route('login');
});

// Dashboard (الصفحة الرئيسية بعد تسجيل الدخول)
Route::get('/dashboard', function () {
    return redirect()->route('posts.index');
})->middleware(['auth', 'verified'])->name('dashboard');

// Routes محمية بالمصادقة
Route::middleware('auth')->group(function () {
    // Profile routes
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');

    // Posts routes
    Route::resource('posts', PostController::class);
});

require __DIR__.'/auth.php';
