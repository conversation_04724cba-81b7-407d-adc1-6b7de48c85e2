<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Post extends Model
{
    use HasFactory;

    // عشان نسمح بالـ mass assignment على هذي الحقول
    protected $fillable = ['title', 'description', 'user_id'];

    /**
     * العلاقة مع User - كل مقال ينتمي لمستخدم واحد
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
}
