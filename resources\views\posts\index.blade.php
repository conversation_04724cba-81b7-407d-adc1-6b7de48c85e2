<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Posts') }}
            </h2>
            <a href="{{ route('posts.create') }}"
               class="bg-slate-800 hover:bg-slate-900 text-white font-bold py-3 px-8 rounded-lg shadow-lg transition-all duration-200 uppercase tracking-wider text-sm">
                CREATE POST
            </a>
        </div>
    </x-slot>

    <div class="py-12 bg-gradient-to-br from-slate-100 via-blue-50 to-indigo-100 min-h-screen">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            @if(session('success'))
                <div class="mb-6 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
                    {{ session('success') }}
                </div>
            @endif

            <!-- زر إضافي للإنشاء -->
            <div class="mb-8 text-center">
                <a href="{{ route('posts.create') }}"
                   class="inline-block bg-slate-800 hover:bg-slate-900 text-white font-bold py-4 px-12 rounded-lg shadow-xl transition-all duration-200 uppercase tracking-wider text-lg">
                    CREATE NEW POST
                </a>
            </div>

            @if($posts->count() > 0)
                <div class="space-y-6">
                    @foreach ($posts as $post)
                        @php
                            $colors = [
                                'bg-gradient-to-br from-blue-50 to-indigo-100 border-blue-200',
                                'bg-gradient-to-br from-green-50 to-emerald-100 border-green-200',
                                'bg-gradient-to-br from-purple-50 to-violet-100 border-purple-200',
                                'bg-gradient-to-br from-pink-50 to-rose-100 border-pink-200',
                                'bg-gradient-to-br from-yellow-50 to-amber-100 border-yellow-200',
                                'bg-gradient-to-br from-cyan-50 to-teal-100 border-cyan-200'
                            ];
                            $colorClass = $colors[$loop->index % count($colors)];
                        @endphp
                        <div class="{{ $colorClass }} overflow-hidden shadow-lg rounded-xl hover:shadow-xl transition-all duration-300">
                            <div class="p-8">
                                <h3 class="text-xl font-bold text-gray-800 mb-3">{{ $post->title }}</h3>
                                <p class="text-gray-700 mb-4 leading-relaxed">{{ Str::limit($post->description, 150) }}</p>

                                <!-- معلومات المؤلف -->
                                <div class="flex items-center mb-4">
                                    <div class="w-8 h-8 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full flex items-center justify-center text-white font-bold text-sm mr-3">
                                        {{ substr($post->user->name ?? 'U', 0, 1) }}
                                    </div>
                                    <div>
                                        <p class="text-sm font-medium text-gray-800">{{ $post->user->name ?? 'Unknown Author' }}</p>
                                        <p class="text-xs text-gray-500">{{ $post->created_at->diffForHumans() }}</p>
                                    </div>
                                </div>

                                <div class="flex items-center justify-between">

                                    <div class="flex items-center space-x-3">
                                        <a href="{{ route('posts.show', $post) }}"
                                           class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg text-sm font-bold transition-all duration-200 shadow-md hover:shadow-lg transform hover:scale-105">
                                            👁️ View
                                        </a>

                                        <a href="{{ route('posts.edit', $post) }}"
                                           class="bg-amber-600 hover:bg-amber-700 text-white px-4 py-2 rounded-lg text-sm font-bold transition-all duration-200 shadow-md hover:shadow-lg transform hover:scale-105">
                                            ✏️ Edit
                                        </a>

                                        <form action="{{ route('posts.destroy', $post) }}" method="POST" class="inline">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit"
                                                    class="bg-rose-600 hover:bg-rose-700 text-black px-4 py-2 rounded-lg text-sm font-bold transition-all duration-200 shadow-md hover:shadow-lg transform hover:scale-105"
                                                    onclick="return confirm('Are you sure you want to delete this post?')">
                                                🗑️ Delete
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>

                <div class="mt-6">
                    {{ $posts->links() }}
                </div>
            @else
                <div class="text-center py-16">
                    <div class="bg-gradient-to-br from-purple-50 to-pink-100 shadow-xl rounded-2xl p-12 max-w-md mx-auto border border-purple-200">
                        <div class="text-6xl mb-6">📝</div>
                        <h3 class="text-2xl font-bold text-gray-800 mb-4">No posts yet</h3>
                        <p class="text-gray-600 mb-8 text-lg">Start by creating your first amazing post!</p>
                        <a href="{{ route('posts.create') }}"
                           class="bg-slate-800 hover:bg-slate-900 text-white font-bold py-4 px-10 rounded-lg shadow-xl transition-all duration-200 uppercase tracking-wider text-lg">
                            CREATE YOUR FIRST POST
                        </a>
                    </div>
                </div>
            @endif
        </div>
    </div>
</x-app-layout>
