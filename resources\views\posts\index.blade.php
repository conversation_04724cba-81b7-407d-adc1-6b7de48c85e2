<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Posts') }}
            </h2>
            <a href="{{ route('posts.create') }}"
               class="bg-slate-800 hover:bg-slate-900 text-white font-medium py-2 px-4 rounded transition-colors text-sm">
                Create Post
            </a>
        </div>
    </x-slot>

    <div class="py-12 bg-gray-50 min-h-screen">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            @if(session('success'))
                <div class="mb-6 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
                    {{ session('success') }}
                </div>
            @endif

            <!-- زر إضافي للإنشاء -->
            <div class="mb-6 text-center">
                <a href="{{ route('posts.create') }}"
                   class="inline-block bg-slate-800 hover:bg-slate-900 text-white font-medium py-3 px-8 rounded transition-colors">
                    Create New Post
                </a>
            </div>

            @if($posts->count() > 0)
                <div class="space-y-4">
                    @foreach ($posts as $post)
                        <div class="bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200">
                            <div class="p-6">
                                <h3 class="text-lg font-semibold text-gray-900 mb-2">{{ $post->title }}</h3>
                                <p class="text-gray-600 mb-4">{{ Str::limit($post->description, 120) }}</p>

                                <!-- معلومات المؤلف -->
                                <div class="flex items-center justify-between">
                                    <div class="text-sm text-gray-500">
                                        Created by <span class="font-medium text-gray-700">{{ $post->user->name ?? 'Unknown Author' }}</span>
                                        • {{ $post->created_at->diffForHumans() }}
                                    </div>

                                    <div class="flex items-center space-x-2">
                                        <a href="{{ route('posts.show', $post) }}"
                                           class="bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded text-sm transition-colors">
                                            View
                                        </a>

                                        <a href="{{ route('posts.edit', $post) }}"
                                           class="bg-yellow-500 hover:bg-yellow-600 text-white px-3 py-1 rounded text-sm transition-colors">
                                            Edit
                                        </a>

                                        <form action="{{ route('posts.destroy', $post) }}" method="POST" class="inline">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit"
                                                    class="bg-red-500 hover:bg-red-600 text-white px-3 py-1 rounded text-sm transition-colors"
                                                    onclick="return confirm('Are you sure you want to delete this post?')">
                                                Delete
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>

                <div class="mt-6">
                    {{ $posts->links() }}
                </div>
            @else
                <div class="text-center py-12">
                    <div class="bg-white border border-gray-200 rounded-lg p-8 max-w-md mx-auto">
                        <div class="text-4xl mb-4">📝</div>
                        <h3 class="text-xl font-semibold text-gray-800 mb-2">No posts yet</h3>
                        <p class="text-gray-600 mb-6">Start by creating your first post!</p>
                        <a href="{{ route('posts.create') }}"
                           class="bg-slate-800 hover:bg-slate-900 text-white font-medium py-2 px-6 rounded transition-colors">
                            Create Your First Post
                        </a>
                    </div>
                </div>
            @endif
        </div>
    </div>
</x-app-layout>
