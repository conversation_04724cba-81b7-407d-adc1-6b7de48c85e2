<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            <?php echo e($post->title); ?>

        </h2>
     <?php $__env->endSlot(); ?>

    <div class="py-12 bg-gradient-to-br from-slate-100 via-purple-50 to-violet-100 min-h-screen">
        <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-6">
                    <div class="mb-6">
                        <h1 class="text-3xl font-bold text-gray-900 mb-4"><?php echo e($post->title); ?></h1>
                        <!-- معلومات المؤلف والتاريخ -->
                        <div class="flex items-center justify-between mb-6 pb-4 border-b border-gray-200">
                            <div class="flex items-center">
                                <div class="w-12 h-12 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full flex items-center justify-center text-white font-bold text-lg mr-4">
                                    <?php echo e(substr($post->user->name ?? 'U', 0, 1)); ?>

                                </div>
                                <div>
                                    <p class="font-semibold text-gray-800"><?php echo e($post->user->name ?? 'Unknown Author'); ?></p>
                                    <p class="text-sm text-gray-500"><?php echo e($post->created_at->format('F j, Y \a\t g:i A')); ?> (<?php echo e($post->created_at->diffForHumans()); ?>)</p>
                                </div>
                            </div>
                            <div class="text-sm text-gray-500">
                                <?php echo e(str_word_count($post->description)); ?> words • <?php echo e(ceil(str_word_count($post->description) / 200)); ?> min read
                            </div>
                        </div>

                        <div class="prose max-w-none">
                            <p class="text-gray-700 leading-relaxed whitespace-pre-line text-lg"><?php echo e($post->description); ?></p>
                        </div>
                    </div>

                    <div class="flex items-center space-x-4 pt-6 border-t border-gray-200">
                        <a href="<?php echo e(route('posts.index')); ?>"
                           class="bg-gray-300 hover:bg-gray-400 text-black font-bold py-2 px-4 rounded-lg transition-colors duration-200">
                            Back to Posts
                        </a>

                        <a href="<?php echo e(route('posts.edit', $post)); ?>"
                           class="bg-blue-600 hover:bg-blue-700 text-black font-bold py-2 px-4 rounded-lg transition-colors duration-200">
                            Edit Post
                        </a>

                        <form action="<?php echo e(route('posts.destroy', $post)); ?>" method="POST" class="inline">
                            <?php echo csrf_field(); ?>
                            <?php echo method_field('DELETE'); ?>
                            <button type="submit"
                                    class="bg-red-600 hover:bg-red-700 text-black font-bold py-2 px-4 rounded-lg transition-colors duration-200"
                                    onclick="return confirm('Are you sure you want to delete this post?')">
                                Delete Post
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\Desktop\taskk_iti\taskk_iti\resources\views/posts/show.blade.php ENDPATH**/ ?>