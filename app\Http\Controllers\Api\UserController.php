<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\UserResource;
use App\Http\Resources\PostResource;
use App\Models\User;
use Illuminate\Http\Request;

class UserController extends Controller
{
    /**
     * جلب معلومات المستخدم الحالي
     */
    public function me(Request $request)
    {
        $user = $request->user()->load('posts');
        return new UserResource($user);
    }

    /**
     * جلب جميع المستخدمين مع عدد مقالاتهم
     */
    public function index()
    {
        $users = User::withCount('posts')->paginate(10);
        return UserResource::collection($users);
    }

    /**
     * جلب مستخدم واحد مع مقالاته
     */
    public function show(User $user)
    {
        $user->load('posts');
        return new UserResource($user);
    }

    /**
     * جلب مقالات مستخدم معين
     */
    public function posts(User $user)
    {
        $posts = $user->posts()->with('user')->paginate(10);
        return PostResource::collection($posts);
    }
}
