<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                <?php echo e(__('Posts')); ?>

            </h2>
            <a href="<?php echo e(route('posts.create')); ?>"
               class="bg-slate-800 hover:bg-slate-900 text-white font-medium py-2 px-4 rounded transition-colors text-sm">
                Create Post
            </a>
        </div>
     <?php $__env->endSlot(); ?>

    <div class="py-12 bg-gray-50 min-h-screen">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <?php if(session('success')): ?>
                <div class="mb-6 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
                    <?php echo e(session('success')); ?>

                </div>
            <?php endif; ?>

            <!-- زر إضافي للإنشاء -->
            <div class="mb-6 text-center">
                <a href="<?php echo e(route('posts.create')); ?>"
                   class="inline-block bg-slate-800 hover:bg-slate-900 text-white font-medium py-3 px-8 rounded transition-colors">
                    Create New Post
                </a>
            </div>

            <?php if($posts->count() > 0): ?>
                <div class="space-y-4">
                    <?php $__currentLoopData = $posts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $post): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200">
                            <div class="p-6">
                                <h3 class="text-lg font-semibold text-gray-900 mb-2"><?php echo e($post->title); ?></h3>
                                <p class="text-gray-600 mb-4"><?php echo e(Str::limit($post->description, 120)); ?></p>

                                <!-- معلومات المؤلف -->
                                <div class="flex items-center justify-between">
                                    <div class="text-sm text-gray-500">
                                        Created by <span class="font-medium text-gray-700"><?php echo e($post->user->name ?? 'Unknown Author'); ?></span>
                                        • <?php echo e($post->created_at->diffForHumans()); ?>

                                    </div>

                                    <div class="flex items-center space-x-2">
                                        <a href="<?php echo e(route('posts.show', $post)); ?>"
                                           class="bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded text-sm transition-colors">
                                            View
                                        </a>

                                        <a href="<?php echo e(route('posts.edit', $post)); ?>"
                                           class="bg-yellow-500 hover:bg-yellow-600 text-white px-3 py-1 rounded text-sm transition-colors">
                                            Edit
                                        </a>

                                        <form action="<?php echo e(route('posts.destroy', $post)); ?>" method="POST" class="inline">
                                            <?php echo csrf_field(); ?>
                                            <?php echo method_field('DELETE'); ?>
                                            <button type="submit"
                                                    class="bg-red-500 hover:bg-red-600 text-white px-3 py-1 rounded text-sm transition-colors"
                                                    onclick="return confirm('Are you sure you want to delete this post?')">
                                                Delete
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>

                <div class="mt-6">
                    <?php echo e($posts->links()); ?>

                </div>
            <?php else: ?>
                <div class="text-center py-12">
                    <div class="bg-white border border-gray-200 rounded-lg p-8 max-w-md mx-auto">
                        <div class="text-4xl mb-4">📝</div>
                        <h3 class="text-xl font-semibold text-gray-800 mb-2">No posts yet</h3>
                        <p class="text-gray-600 mb-6">Start by creating your first post!</p>
                        <a href="<?php echo e(route('posts.create')); ?>"
                           class="bg-slate-800 hover:bg-slate-900 text-white font-medium py-2 px-6 rounded transition-colors">
                            Create Your First Post
                        </a>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\Desktop\taskk_iti\taskk_iti\resources\views/posts/index.blade.php ENDPATH**/ ?>