<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\PostController;
use App\Http\Controllers\Api\UserController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
*/

// ---------------------------
// API Routes (محمي بـ Sanctum)
// ---------------------------
Route::middleware('auth:sanctum')->group(function () {

    // User Routes
    Route::get('/user', [UserController::class, 'me']);              // معلومات المستخدم الحالي
    Route::get('/users', [UserController::class, 'index']);          // جلب جميع المستخدمين
    Route::get('/users/{user}', [UserController::class, 'show']);    // جلب مستخدم واحد
    Route::get('/users/{user}/posts', [UserController::class, 'posts']); // مقالات مستخدم معين

    // Posts Routes
    Route::get('/posts', [PostController::class, 'apiIndex']);       // جلب كل البوستات
    Route::get('/posts/{post}', [PostController::class, 'apiShow']); // جلب بوست واحد
    Route::post('/posts', [PostController::class, 'apiStore']);      // إنشاء بوست جديد
});
