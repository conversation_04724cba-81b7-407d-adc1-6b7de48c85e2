<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\PostController;
use App\Http\Controllers\Api\UserController;
use App\Http\Controllers\Api\AuthController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
*/

// ---------------------------
// Public Authentication Routes
// ---------------------------

Route::post('/login', [AuthController::class, 'login']);
Route::post('/register', [AuthController::class, 'register']);

// ---------------------------
// API Routes (محمي بـ Sanctum)
// ---------------------------
Route::middleware('auth:sanctum')->group(function () {

    // Authentication Routes
    Route::post('/logout', [AuthController::class, 'logout']);
    Route::get('/me', [AuthController::class, 'me']);

    // User Routes
    Route::get('/user', [UserController::class, 'me']);              // معلومات المستخدم الحالي
    Route::get('/users', [UserController::class, 'index']);          // جلب جميع المستخدمين
    Route::get('/users/{user}', [UserController::class, 'show']);    // جلب مستخدم واحد
    Route::get('/users/{user}/posts', [UserController::class, 'posts']); // مقالات مستخدم معين

    // Posts Routes
    Route::get('/posts', [PostController::class, 'apiIndex']);       // جلب كل البوستات
    Route::get('/posts/{post}', [PostController::class, 'apiShow']); // جلب بوست واحد
    Route::post('/posts', [PostController::class, 'apiStore']);      // إنشاء بوست جديد
});
