<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Str;

class PostResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'description' => $this->description,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,

            // معلومات المؤلف
            'author' => new UserResource($this->whenLoaded('user')),

            // معلومات إضافية
            'excerpt' => $this->when($request->routeIs('api.posts.index'),
                Str::limit($this->description, 100)
            ),
            'word_count' => str_word_count($this->description),
            'reading_time' => ceil(str_word_count($this->description) / 200) . ' min read',
        ];
    }
}
