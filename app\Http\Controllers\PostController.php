<?php

namespace App\Http\Controllers;

use App\Models\Post;
use Illuminate\Http\Request;
use App\Http\Requests\StorePostRequest;
use App\Http\Requests\UpdatePostRequest;
use App\Http\Resources\PostResource;
use App\Http\Resources\PostCollection;
use App\Http\Resources\UserResource;

class PostController extends Controller
{
    // جلب كل البوستات للـ API
    public function apiIndex()
    {
        $posts = Post::with('user')->paginate(10);
        return new PostCollection($posts);
    }

    // جلب بوست واحد للـ API
    public function apiShow(Post $post)
    {
        $post->load('user');
        return new PostResource($post);
    }

    // إنشاء بوست جديد للـ API
    public function apiStore(StorePostRequest $request)
    {
        $validated = $request->validated();
        $validated['user_id'] = $request->user()->id; // ربط المقال بالمستخدم المسجل

        $post = Post::create($validated);
        $post->load('user');

        return new PostResource($post);
    }

    // دوال الـ Web (Blade)

    // عرض كل البوستات
    public function index()
    {
        $posts = Post::with('user')->paginate(10);
        return view('posts.index', compact('posts'));
    }

    // عرض صفحة إنشاء بوست جديد
    public function create()
    {
        return view('posts.create');
    }

    // تخزين بوست جديد
    public function store(StorePostRequest $request)
    {
        $validated = $request->validated();
        $validated['user_id'] = $request->user()->id; // ربط المقال بالمستخدم المسجل

        Post::create($validated);
        return redirect()->route('posts.index')->with('success', 'Post created successfully!');
    }

    // عرض بوست واحد
    public function show(Post $post)
    {
        $post->load('user');
        return view('posts.show', compact('post'));
    }

    // عرض صفحة تعديل بوست
    public function edit(Post $post)
    {
        return view('posts.edit', compact('post'));
    }

    // تحديث بوست
    public function update(UpdatePostRequest $request, Post $post)
    {
        $post->update($request->validated());
        return redirect()->route('posts.index')->with('success', 'Post updated successfully!');
    }

    // حذف بوست
    public function destroy(Post $post)
    {
        $post->delete();
        return redirect()->route('posts.index')->with('success', 'Post deleted successfully!');
    }
}
