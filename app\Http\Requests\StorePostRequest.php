<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StorePostRequest extends FormRequest
{
    public function authorize()
    {
        return true; // يسمح بالوصول إذا المستخدم مسجل دخول
    }

    public function rules()
    {
        return [
            'title' => 'required|min:3|unique:posts,title',
            'description' => 'required|min:10',
        ];
    }
}
